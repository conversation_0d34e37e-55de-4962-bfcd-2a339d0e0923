I want to change the use of Chat gpt to use a langgraph flow. we will create a React agent here is an exemple implementation (  only focus on relevant informations e.g how media are passed , some of these may not be helpful for you e.g getContent node, clean up node,.....  ) this example represent the a flow where we download a tiktok video and extract a recipe from it. we want to build a system where an agent receives a message ( text, image,video,... ) and respond. the agent can use tool if necessary (we will handle tool a fter). only focus on the backend ( creating necessary schema, to save messages , sections,...., change the actual implementation with chat gpt to use langgraph with gemini) import { Injectable } from '@nestjs/common';

import { ConfigService } from '@nestjs/config';

import * as Tik<PERSON> from '@tobyg74/tiktok-api-dl';

import z from 'zod';

import {

  AIMessage,

  BaseMessage,

  SystemMessage,

  HumanMessage,

} from '@langchain/core/messages';

import { StateGraph, START, END, Annotation } from '@langchain/langgraph';

import { ToolNode } from '@langchain/langgraph/prebuilt';

import { ChatGoogleGenerativeAI } from '@langchain/google-genai';

import * as fs from 'fs';

import * as path from 'path';

import axios from 'axios';

import { ttdl } from 'btch-downloader';

import { exec } from 'child_process';

import { promisify } from 'util';

  

export interface RecipeGeneratorInput {

  url: string;

  language?: string;

}

  

export interface VideoInfo {

  videoPath: string;

  videoDescription: string;

  durationSeconds?: number; // Video duration in seconds

  downloadMethod: 'toby74' | 'btch'; // Track which downloader was used

  primaryMethodFailed?: boolean; // Track if primary method failed

  fallbackUsed?: boolean; // Track if fallback was necessary

}

  

// Type definition for btch-downloader result

interface BTCHDownloaderResult {

  video?: string[];

  title?: string;

}

  

// Define schemas outside the class for type inference

const ingredientSchema = z.object({

  quantity: z.number(),

  unit: z.string(),

  name: z.string(),

});

  

const stepSchema = z.object({

  name: z

    .string()

    .describe('Name of the step. Should be a short and descriptive name.'),

  description: z

    .string()

    .describe(

      'Detailed description of the step. Should include all substeps and necessary information to perform the step.',

    ),

});

  

// TODO: improve metadata schema

const metadataSchema = z.object({

  audio_helpful: z.boolean().optional(),

  video_type: z.enum(['cinematic', 'vlog', 'tutorial', 'other']).optional(),

});

  

const recipeSchema = z.object({

  title: z.string().min(1).max(200),

  cuisine: z

    .string()

    .optional()

    .describe('Cuisine of the recipe. e.g: Italian, Mexican, Chinese, etc.'),

  category: z

    .string()

    .optional()

    .describe(

      'Category of the recipe. e.g: Main dish, Side dish, Dessert, etc.',

    ),

  description: z

    .string()

    .describe(

      'Short description of the recipe. Should be less than 200 characters.It should be appealing and catchy. keep in the overall description all important information present in the origirnal description of the video (e.g macros,... ).',

    ),

  cooking_time_minutes: z.number().nullable().optional(),

  servings: z.number().nullable().optional(),

  ingredients: z.array(ingredientSchema).default([]),

  steps: z

    .array(stepSchema)

    .default([])

    .describe(

      'List of steps to prepare the recipe. Should include all necessary information to perform the step.',

    ),

  metadata: metadataSchema.optional(),

});

  

// Export the type for use elsewhere

export type RecipeOutput = z.infer<typeof recipeSchema>;

  

export interface RecipeGeneratorResult {

  recipe: RecipeOutput;

  videoInfo?: VideoInfo; // Optional video info for quality checks

}

  

@Injectable()

export class RecipeGeneratorService {

  // System prompt for recipe generation

  private readonly systemPrompt = `

You are an expert culinary assistant.

You will receive a cooking video (e.g., TikTok) and the video description.

Extract a clean, structured recipe from both the video content and the video description, as ingredients and steps may be mentioned in either source.

If there is insufficient info, make reasonable assumptions.

  

Output requirements:

- Keep measurements and quantities if mentioned.

- Keep the order of steps as presented. provide a detailled description of each step.

`;

  

  constructor(private readonly configService: ConfigService) {}

  

  // Helper method to extract video duration using ffprobe

  private async getVideoDuration(videoPath: string): Promise<number> {

    const execAsync = promisify(exec);

    try {

      const command = `ffprobe -v quiet -show_entries format=duration -of csv=p=0 "${videoPath}"`;

      const { stdout } = await execAsync(command);

      const duration = parseFloat(stdout.trim());

      if (isNaN(duration)) {

        console.warn('Could not extract video duration, defaulting to 0');

        return 0;

      }

      return Math.round(duration); // Return duration in seconds, rounded

    } catch (error) {

      console.warn('Error extracting video duration:', error);

      // Fallback: try to extract from TikTok API if available

      return 0;

    }

  }

  

  private AgentState = Annotation.Root({

    messages: Annotation<BaseMessage[]>({

      reducer: (x, y) => x.concat(y),

      default: () => [new SystemMessage(this.systemPrompt)],

    }),

    input: Annotation<RecipeGeneratorInput>(),

    videoInfo: Annotation<VideoInfo>(),

    output: Annotation<RecipeOutput>(),

  });

  

  private mimeFromExt(filePath: string): string {

    const ext = path.extname(filePath).toLowerCase();

    switch (ext) {

      case '.mp4':

        return 'video/mp4';

      case '.mov':

        return 'video/quicktime';

      case '.webm':

        return 'video/webm';

      default:

        return 'application/octet-stream';

    }

  }

  

  private tools = [];

  

  private toolNode = new ToolNode(this.tools);

  

  private getContentNode = async (state: typeof this.AgentState.State) => {

    const url = state.input.url;

    let videoInfo: VideoInfo;

  

    try {

      // Primary method: Use downloadTikTokVideo

      console.log('Attempting to download video using primary method...');

      videoInfo = await this.downloadTikTokVideoToby74(url);

      console.log('Video downloaded successfully using Toby74:', videoInfo.videoPath);

    } catch (primaryError) {

      const primaryErrorMsg =

        primaryError instanceof Error

          ? primaryError.message

          : String(primaryError);

      console.log('Primary download method failed:', primaryErrorMsg);

  

      try {

        console.log('Attempting fallback download method...');

        videoInfo = await this.downloadTiktokVideoBTCH(url);

        console.log('Video downloaded successfully using BTCH fallback:', videoInfo.videoPath);

      } catch (fallbackError) {

        const fallbackErrorMsg =

          fallbackError instanceof Error

            ? fallbackError.message

            : String(fallbackError);

        console.error('Both download methods failed:');

        console.error('Primary error:', primaryErrorMsg);

        console.error('Fallback error:', fallbackErrorMsg);

        throw new Error(`Failed to download video: ${primaryErrorMsg}`);

      }

    }

  

    return { videoInfo };

  };

  

  private shouldContinueNode = (state: typeof this.AgentState.State) => {

    const lastMessage = state.messages[state.messages.length - 1];

  

    if (lastMessage && !(lastMessage as AIMessage).tool_calls?.length) {

      return 'cleanup';

    }

  

    return 'action';

  };

  

  private cleanupNode = async (state: typeof this.AgentState.State) => {

    const videoPath = state.videoInfo?.videoPath;

  

    /* if (videoPath && fs.existsSync(videoPath)) {

      try {

        await fs.promises.unlink(videoPath);

        console.log('Successfully deleted downloaded video:', videoPath);

      } catch (error) {

        console.warn('Failed to delete video file:', videoPath, error);

      }

    } */

  

    return {};

  };

  

  private agentNode = async (state: typeof this.AgentState.State) => {

    const messages = state.messages;

    console.log('Invoking model with messages:', messages.length);

  

    // Get the API key from configuration

    const apiKey = this.configService.get<string>('gemini.apiKey');

    if (!apiKey) {

      throw new Error('GEMINI_API_KEY is not configured');

    }

  

    const gemini = new ChatGoogleGenerativeAI({

      model: 'gemini-2.5-flash-lite',

      temperature: 1,

      maxRetries: 2,

      apiKey: apiKey,

    });

  

    const geminiWithSchema = gemini.withStructuredOutput(recipeSchema);

  

    try {

      // Get the structured response

      const structuredResponse = await geminiWithSchema.invoke(messages);

  

      console.log('Recipe response:', structuredResponse);

  

      return {

        messages: [structuredResponse],

        output: structuredResponse,

      };

    } catch (err) {

      console.error('Model invocation error:', JSON.stringify(err, null, 2));

      return { messages: [new AIMessage('error')] };

    }

  };

  

  private prepareRequestNode = (state: typeof this.AgentState.State) => {

    const videoPath = state.videoInfo.videoPath;

    const videoDescription = state.videoInfo.videoDescription;

  

    // Read and encode the video file as base64

    const videoData = fs.readFileSync(videoPath);

    const base64Video = videoData.toString('base64');

    const mimeType = this.mimeFromExt(videoPath);

  

    // Create the human message with multimodal content

    const content: Array<{

      type: string;

      source_type?: string;

      text?: string;

      mime_type?: string;

      data?: string;

    }> = [

      {

        type: 'text',

        text: 'Extract a clean, structured and detailed recipe from this cooking video.',

      },

      {

        type: 'file',

        mime_type: mimeType,

        source_type: 'base64',

        data: base64Video,

      },

    ];

  

    // Add video description if available

    if (videoDescription && videoDescription.trim()) {

      content.splice(1, 0, {

        type: 'text',

        text: `Video description (may include ingredients/steps):\n${videoDescription}`,

      });

    }

  

    const humanMessage = new HumanMessage({

      content: content,

    });

  

    return {

      messages: [humanMessage],

      videoInfo: state.videoInfo,

    };

  };

  

  // Getter methods for accessing schemas and prompts

  getRecipeSchema() {

    return recipeSchema;

  }

  

  getSystemPrompt(): string {

    return this.systemPrompt;

  }

  

  // Validate recipe data against schema

  validateRecipe(data: unknown): RecipeOutput {

    return recipeSchema.parse(data);

  }

  

  async downloadTikTokVideoToby74(url: string): Promise<VideoInfo> {

    if (!url) throw new Error('Missing TikTok URL');

    const outDir = path.resolve('downloads');

    await fs.promises.mkdir(outDir, { recursive: true });

    console.log('Download directory:', outDir);

  

    // Explicitly use version v1 to get TiktokAPIResponse with Content type

    const result = await Tiktok.Downloader(url, { version: 'v1' });

    if (result?.status !== 'success' || !result?.result) {

      throw new Error('TikTok API DL did not return success');

    }

  

    const videoDescription = result.result.desc ?? '';

  

    // Type guard to ensure we have video data

    if (!result.result.video || !result.result.video.playAddr) {

      throw new Error('No video data found for this TikTok');

    }

  

    const playAddr = result.result.video.playAddr;

    if (!Array.isArray(playAddr) || playAddr.length === 0) {

      throw new Error('No video URL found for this TikTok');

    }

    const videoUrl = playAddr[1] || playAddr[0];

  

    const fileName = `tiktok_video_${Date.now()}.mp4`;

    const videoPath = path.join(outDir, fileName);

  

    const response = await axios.get(videoUrl, { responseType: 'stream' });

    await new Promise<void>((resolvePromise, reject) => {

      const writer = fs.createWriteStream(videoPath);

      (response.data as NodeJS.ReadableStream).pipe(writer);

      writer.on('finish', () => resolvePromise());

      writer.on('error', reject);

    });

  

    // Extract video duration

    const durationSeconds = await this.getVideoDuration(videoPath);

  

    return {

      videoPath,

      videoDescription,

      durationSeconds,

      downloadMethod: 'toby74',

      primaryMethodFailed: false,

      fallbackUsed: false

    };

  }

  

  async downloadTiktokVideoBTCH(url: string): Promise<VideoInfo> {

    let videoPath = '';

    let videoDescription = '';

  

    console.log('Attempting fallback download method...');

    const btchResult = (await ttdl(url)) as BTCHDownloaderResult;

  

    if (btchResult && btchResult.video && btchResult.video.length > 0) {

      const videoUrl = btchResult.video[0];

      videoDescription = btchResult.title || '';

  

      // Download the video from the URL

      const outDir = path.resolve('downloads');

      await fs.promises.mkdir(outDir, { recursive: true });

  

      const fileName = `tiktok_video_fallback_${Date.now()}.mp4`;

      videoPath = path.join(outDir, fileName);

  

      const response = await axios.get(videoUrl, { responseType: 'stream' });

      await new Promise<void>((resolvePromise, reject) => {

        const writer = fs.createWriteStream(videoPath);

        (response.data as NodeJS.ReadableStream).pipe(writer);

        writer.on('finish', () => resolvePromise());

        writer.on('error', reject);

      });

  

      // Extract video duration

      const durationSeconds = await this.getVideoDuration(videoPath);

  

      console.log('Fallback video downloaded successfully:', videoPath);

      return {

        videoPath,

        videoDescription,

        durationSeconds,

        downloadMethod: 'btch',

        primaryMethodFailed: true,

        fallbackUsed: true

      };

    } else {

      throw new Error('No video URL found in btch-downloader result');

    }

  }

  

  // Main recipe generation method (to be implemented)

  async generateRecipe({

    url,

    language,

  }: RecipeGeneratorInput): Promise<RecipeOutput> {

    const result = await this.generateRecipeWithVideoInfo({ url, language });

    return result.recipe;

  }

  

  // Enhanced method that returns both recipe and video info for quality checks

  async generateRecipeWithVideoInfo({

    url,

    language,

  }: RecipeGeneratorInput): Promise<RecipeGeneratorResult> {

    const workflow = new StateGraph(this.AgentState)

      .addNode('getContent', this.getContentNode)

      .addNode('prepareRequest', this.prepareRequestNode)

      .addNode('agent', this.agentNode)

      .addNode('action', this.toolNode)

      .addNode('cleanup', this.cleanupNode)

      .addEdge(START, 'getContent')

      .addEdge('getContent', 'prepareRequest')

      .addEdge('prepareRequest', 'agent')

      .addEdge('action', 'agent')

      .addEdge('cleanup', END)

      .addConditionalEdges('agent', this.shouldContinueNode);

  

    const agent = workflow.compile();

  

    try {

      const result = await agent.invoke({

        messages: [],

        input: {

          url,

          language,

        },

        videoInfo: {

          videoPath: '',

          videoDescription: '',

          durationSeconds: 0,

          downloadMethod: 'toby74',

          primaryMethodFailed: false,

          fallbackUsed: false

        },

        output: undefined,

      });

  

      return {

        recipe: result.output,

        videoInfo: result.videoInfo,

      };

    } catch (err) {

      console.error('Error generating recipe:', err);

      throw err;

    }

  }

}



